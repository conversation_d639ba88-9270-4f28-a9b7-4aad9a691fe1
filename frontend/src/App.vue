<script setup>
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import AppLayout from './components/AppLayout.vue'
import Login from './views/Login.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

onMounted(() => {
  // Initialize authentication
  authStore.initializeAuth()

  // Check if route requires auth
  if (route.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    router.push('/login')
  }
})
</script>

<template>
  <div id="app">
    <!-- Show login page if not authenticated -->
    <Login v-if="!authStore.isAuthenticated && route.name === 'Login'" />

    <!-- Show main layout if authenticated -->
    <AppLayout v-else-if="authStore.isAuthenticated" />

    <!-- Redirect to login if not authenticated and not on login page -->
    <div v-else class="loading">
      <el-loading-service />
    </div>
  </div>
</template>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
