<template>
  <div class="dashboard">
    <el-row :gutter="20" class="dashboard-header">
      <el-col :span="24">
        <h1>Welcome to AI Partner Dashboard</h1>
        <p>Manage your AI-powered software company simulation</p>
      </el-col>
    </el-row>

    <!-- Stats Cards -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon projects">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.projects }}</h3>
              <p>Active Projects</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon tasks">
              <el-icon><List /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.tasks }}</h3>
              <p>Open Tasks</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon team">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.teamMembers }}</h3>
              <p>Team Members</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon requests">
              <el-icon><Plus /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ stats.headcountRequests }}</h3>
              <p>Pending Requests</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Recent Activity -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>Recent Projects</span>
              <el-button type="primary" size="small" @click="$router.push('/projects')">
                View All
              </el-button>
            </div>
          </template>
          <div v-if="recentProjects.length === 0" class="empty-state">
            <p>No projects yet. Create your first project!</p>
          </div>
          <div v-else>
            <div v-for="project in recentProjects" :key="project.id" class="project-item">
              <h4>{{ project.title }}</h4>
              <p>{{ project.description }}</p>
              <el-tag :type="getStatusType(project.status)">{{ project.status }}</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>Team Overview</span>
              <el-button type="primary" size="small" @click="$router.push('/team')">
                Manage Team
              </el-button>
            </div>
          </template>
          <div v-if="teamMembers.length === 0" class="empty-state">
            <p>No team members yet.</p>
          </div>
          <div v-else>
            <div v-for="member in teamMembers" :key="member.id" class="team-member-item">
              <div class="member-info">
                <h4>{{ member.name }}</h4>
                <p>{{ member.ai_role_name }} - {{ member.specialization }}</p>
              </div>
              <div class="member-status">
                <el-progress 
                  :percentage="member.workload_percentage" 
                  :color="getWorkloadColor(member.workload_percentage)"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Folder, List, User, Plus } from '@element-plus/icons-vue'
import api from '../services/api.js'

const stats = ref({
  projects: 0,
  tasks: 0,
  teamMembers: 0,
  headcountRequests: 0
})

const recentProjects = ref([])
const teamMembers = ref([])

const getStatusType = (status) => {
  const types = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'ON_HOLD': 'danger'
  }
  return types[status] || 'info'
}

const getWorkloadColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const fetchDashboardData = async () => {
  try {
    // Fetch projects
    const projectsResponse = await api.get('/projects/')
    recentProjects.value = projectsResponse.data.results.slice(0, 3)
    stats.value.projects = projectsResponse.data.count

    // Fetch team members
    const teamResponse = await api.get('/team-members/')
    teamMembers.value = teamResponse.data.results.slice(0, 4)
    stats.value.teamMembers = teamResponse.data.count

    // Fetch headcount requests
    const requestsResponse = await api.get('/headcount-requests/')
    stats.value.headcountRequests = requestsResponse.data.results.filter(r => r.status === 'PENDING').length

    // Fetch tasks (if available)
    try {
      const tasksResponse = await api.get('/tasks/')
      stats.value.tasks = tasksResponse.data.results.filter(t => t.status !== 'COMPLETED').length
    } catch (error) {
      console.log('Tasks endpoint not available yet')
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error)
  }
}

onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 20px;
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.dashboard-header p {
  margin: 0;
  color: #606266;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 100px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stats-icon.projects {
  background-color: #409EFF;
}

.stats-icon.tasks {
  background-color: #67C23A;
}

.stats-icon.team {
  background-color: #E6A23C;
}

.stats-icon.requests {
  background-color: #F56C6C;
}

.stats-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.stats-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-item, .team-member-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.project-item:last-child, .team-member-item:last-child {
  border-bottom: none;
}

.project-item h4, .team-member-item h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.project-item p, .team-member-item p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.team-member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  flex: 1;
}

.member-status {
  width: 120px;
}

.empty-state {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
