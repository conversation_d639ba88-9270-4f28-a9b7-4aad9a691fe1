<template>
  <div class="headcount-requests">
    <div class="page-header">
      <h1>Headcount Requests</h1>
      <p>Review AI-generated team expansion requests</p>
    </div>

    <!-- Requests List -->
    <el-card>
      <el-table :data="requests" v-loading="loading">
        <el-table-column prop="project_title" label="Project" min-width="150" />
        <el-table-column prop="ai_role_name" label="Role Needed" width="150" />
        <el-table-column prop="ai_role_experience" label="Experience" width="120" />
        <el-table-column prop="urgency" label="Urgency" width="100">
          <template #default="{ row }">
            <el-tag :type="getUrgencyType(row.urgency)">{{ row.urgency }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="budget_impact" label="Budget Impact" width="120">
          <template #default="{ row }">
            ${{ row.budget_impact.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="Status" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="requested_at" label="Requested" width="120">
          <template #default="{ row }">
            {{ formatDate(row.requested_at) }}
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="150">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'PENDING'" 
              size="small" 
              @click="viewRequest(row)"
            >
              Review
            </el-button>
            <el-button v-else size="small" @click="viewRequest(row)">View</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Request Detail Dialog -->
    <el-dialog v-model="showDetailDialog" title="Headcount Request Details" width="700px">
      <div v-if="selectedRequest">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="Project">{{ selectedRequest.project_title }}</el-descriptions-item>
          <el-descriptions-item label="Role">{{ selectedRequest.ai_role_name }}</el-descriptions-item>
          <el-descriptions-item label="Experience Level">{{ selectedRequest.ai_role_experience }}</el-descriptions-item>
          <el-descriptions-item label="Urgency">
            <el-tag :type="getUrgencyType(selectedRequest.urgency)">{{ selectedRequest.urgency }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Budget Impact">${{ selectedRequest.budget_impact.toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="Duration">{{ formatDuration(selectedRequest.estimated_duration) }}</el-descriptions-item>
        </el-descriptions>

        <el-divider />

        <h3>AI Justification</h3>
        <p class="justification">{{ selectedRequest.justification }}</p>

        <h3>Required Skills</h3>
        <el-tag v-for="skill in selectedRequest.required_skills" :key="skill" style="margin-right: 8px;">
          {{ skill }}
        </el-tag>

        <div v-if="selectedRequest.status === 'PENDING'" class="approval-section">
          <el-divider />
          <h3>CEO Response</h3>
          <el-form :model="approvalForm" ref="approvalFormRef">
            <el-form-item label="Decision" prop="approved">
              <el-radio-group v-model="approvalForm.approved">
                <el-radio :label="true">Approve</el-radio>
                <el-radio :label="false">Reject</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="Response" prop="ceo_response">
              <el-input 
                v-model="approvalForm.ceo_response" 
                type="textarea" 
                rows="3"
                placeholder="Enter your response..."
              />
            </el-form-item>
          </el-form>
        </div>

        <div v-else-if="selectedRequest.ceo_response">
          <el-divider />
          <h3>CEO Response</h3>
          <p class="ceo-response">{{ selectedRequest.ceo_response }}</p>
        </div>
      </div>

      <template #footer>
        <div v-if="selectedRequest?.status === 'PENDING'">
          <el-button @click="showDetailDialog = false">Cancel</el-button>
          <el-button 
            type="primary" 
            @click="submitApproval" 
            :loading="submitting"
          >
            Submit Decision
          </el-button>
        </div>
        <div v-else>
          <el-button @click="showDetailDialog = false">Close</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../services/api.js'

const requests = ref([])
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedRequest = ref(null)
const submitting = ref(false)
const approvalFormRef = ref()

const approvalForm = reactive({
  approved: null,
  ceo_response: ''
})

const getUrgencyType = (urgency) => {
  const types = {
    'LOW': 'info',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
    'CRITICAL': 'danger'
  }
  return types[urgency] || 'info'
}

const getStatusType = (status) => {
  const types = {
    'PENDING': 'warning',
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'FULFILLED': 'success'
  }
  return types[status] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const formatDuration = (duration) => {
  // Simple duration formatting - assumes format like "P30D" for 30 days
  if (duration.includes('D')) {
    const days = duration.match(/(\d+)D/)?.[1]
    return days ? `${days} days` : duration
  }
  return duration
}

const fetchRequests = async () => {
  loading.value = true
  try {
    const response = await api.get('/headcount-requests/')
    requests.value = response.data.results
  } catch (error) {
    console.error('Error fetching headcount requests:', error)
    ElMessage.error('Failed to fetch headcount requests')
  } finally {
    loading.value = false
  }
}

const viewRequest = (request) => {
  selectedRequest.value = request
  showDetailDialog.value = true
  
  // Reset approval form
  approvalForm.approved = null
  approvalForm.ceo_response = ''
}

const submitApproval = async () => {
  if (approvalForm.approved === null) {
    ElMessage.warning('Please select approve or reject')
    return
  }
  
  if (!approvalForm.ceo_response.trim()) {
    ElMessage.warning('Please enter a response')
    return
  }

  submitting.value = true
  try {
    await api.post(`/headcount-requests/${selectedRequest.value.id}/approve/`, approvalForm)
    ElMessage.success('Decision submitted successfully!')
    showDetailDialog.value = false
    fetchRequests()
  } catch (error) {
    console.error('Error submitting approval:', error)
    ElMessage.error('Failed to submit decision')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  fetchRequests()
})
</script>

<style scoped>
.headcount-requests {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.justification, .ceo-response {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  white-space: pre-wrap;
  line-height: 1.6;
}

.approval-section {
  margin-top: 20px;
}
</style>
