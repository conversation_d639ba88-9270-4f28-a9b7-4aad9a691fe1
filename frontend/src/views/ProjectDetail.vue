<template>
  <div class="project-detail">
    <el-page-header @back="$router.go(-1)" :content="project?.title || 'Project Detail'" />
    
    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="project" class="project-content">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card title="Project Information">
            <h2>{{ project.title }}</h2>
            <p>{{ project.description }}</p>
            
            <el-divider />
            
            <h3>Requirements</h3>
            <p class="requirements">{{ project.requirements }}</p>
            
            <el-divider />
            
            <h3>Progress</h3>
            <el-progress :percentage="project.progress_percentage" :stroke-width="8" />
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card title="Project Details">
            <div class="detail-item">
              <strong>Status:</strong>
              <el-tag :type="getStatusType(project.status)">{{ project.status }}</el-tag>
            </div>
            <div class="detail-item">
              <strong>Priority:</strong>
              <el-tag :type="getPriorityType(project.priority)">{{ project.priority }}</el-tag>
            </div>
            <div class="detail-item">
              <strong>Created:</strong>
              {{ formatDate(project.created_at) }}
            </div>
            <div class="detail-item" v-if="project.due_date">
              <strong>Due Date:</strong>
              {{ formatDate(project.due_date) }}
            </div>
          </el-card>
          
          <el-card title="Team Members" style="margin-top: 20px;">
            <div v-if="project.team_members?.length === 0" class="empty-state">
              No team members assigned yet.
            </div>
            <div v-else>
              <div v-for="member in project.team_members" :key="member.id" class="team-member">
                <strong>{{ member.name }}</strong>
                <p>{{ member.role }} ({{ member.allocation_percentage }}%)</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import api from '../services/api.js'

const route = useRoute()
const project = ref(null)
const loading = ref(true)

const getStatusType = (status) => {
  const types = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'ON_HOLD': 'danger',
    'CANCELLED': 'info'
  }
  return types[status] || 'info'
}

const getPriorityType = (priority) => {
  const types = {
    'LOW': 'info',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
    'CRITICAL': 'danger'
  }
  return types[priority] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const fetchProject = async () => {
  try {
    const response = await api.get(`/projects/${route.params.id}/`)
    project.value = response.data
  } catch (error) {
    console.error('Error fetching project:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchProject()
})
</script>

<style scoped>
.project-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.project-content {
  margin-top: 20px;
}

.requirements {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-member {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.team-member:last-child {
  border-bottom: none;
}

.team-member strong {
  display: block;
  margin-bottom: 5px;
}

.team-member p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
