<template>
  <div class="tasks">
    <div class="page-header">
      <h1>Tasks</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        New Task
      </el-button>
    </div>

    <!-- Tasks List -->
    <el-card>
      <el-table :data="tasks" v-loading="loading">
        <el-table-column prop="title" label="Title" min-width="200" />
        <el-table-column prop="project_title" label="Project" width="150" />
        <el-table-column prop="status" label="Status" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="Priority" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assigned_to" label="Assigned To" width="150">
          <template #default="{ row }">
            <span v-if="row.assigned_to">{{ row.assigned_to.name }}</span>
            <span v-else class="unassigned">Unassigned</span>
          </template>
        </el-table-column>
        <el-table-column prop="estimated_hours" label="Est. Hours" width="100" />
        <el-table-column prop="progress_percentage" label="Progress" width="120">
          <template #default="{ row }">
            <el-progress :percentage="row.progress_percentage" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column prop="due_date" label="Due Date" width="120">
          <template #default="{ row }">
            <span v-if="row.due_date" :class="{ 'overdue': row.is_overdue }">
              {{ formatDate(row.due_date) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewTask(row)">View</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Create Task Dialog -->
    <el-dialog v-model="showCreateDialog" title="Create New Task" width="600px">
      <el-form :model="newTask" :rules="taskRules" ref="taskFormRef">
        <el-form-item label="Title" prop="title">
          <el-input v-model="newTask.title" />
        </el-form-item>
        <el-form-item label="Description" prop="description">
          <el-input v-model="newTask.description" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="Project" prop="project">
          <el-select v-model="newTask.project" style="width: 100%">
            <el-option 
              v-for="project in projects" 
              :key="project.id" 
              :label="project.title" 
              :value="project.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Priority" prop="priority">
          <el-select v-model="newTask.priority" style="width: 100%">
            <el-option label="Low" value="LOW" />
            <el-option label="Medium" value="MEDIUM" />
            <el-option label="High" value="HIGH" />
            <el-option label="Critical" value="CRITICAL" />
          </el-select>
        </el-form-item>
        <el-form-item label="Estimated Hours" prop="estimated_hours">
          <el-input-number v-model="newTask.estimated_hours" :min="0.5" :step="0.5" />
        </el-form-item>
        <el-form-item label="Due Date">
          <el-date-picker v-model="newTask.due_date" type="date" style="width: 100%" />
        </el-form-item>
        <el-form-item label="Required Skills">
          <el-input v-model="skillsInput" placeholder="Enter skills separated by commas" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">Cancel</el-button>
        <el-button type="primary" @click="createTask" :loading="creating">Create</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '../services/api.js'

const tasks = ref([])
const projects = ref([])
const loading = ref(false)
const showCreateDialog = ref(false)
const creating = ref(false)
const taskFormRef = ref()
const skillsInput = ref('')

const newTask = reactive({
  title: '',
  description: '',
  project: '',
  priority: 'MEDIUM',
  estimated_hours: 8,
  due_date: null,
  required_skills: []
})

const taskRules = {
  title: [{ required: true, message: 'Please enter task title', trigger: 'blur' }],
  description: [{ required: true, message: 'Please enter task description', trigger: 'blur' }],
  project: [{ required: true, message: 'Please select a project', trigger: 'change' }],
  priority: [{ required: true, message: 'Please select priority', trigger: 'change' }],
  estimated_hours: [{ required: true, message: 'Please enter estimated hours', trigger: 'blur' }]
}

const getStatusType = (status) => {
  const types = {
    'TODO': 'info',
    'IN_PROGRESS': 'warning',
    'IN_REVIEW': 'primary',
    'BLOCKED': 'danger',
    'COMPLETED': 'success'
  }
  return types[status] || 'info'
}

const getPriorityType = (priority) => {
  const types = {
    'LOW': 'info',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
    'CRITICAL': 'danger'
  }
  return types[priority] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const fetchTasks = async () => {
  loading.value = true
  try {
    const response = await api.get('/tasks/')
    tasks.value = response.data.results
  } catch (error) {
    console.error('Error fetching tasks:', error)
    ElMessage.error('Failed to fetch tasks')
  } finally {
    loading.value = false
  }
}

const fetchProjects = async () => {
  try {
    const response = await api.get('/projects/')
    projects.value = response.data.results
  } catch (error) {
    console.error('Error fetching projects:', error)
  }
}

const createTask = async () => {
  if (!taskFormRef.value) return
  
  await taskFormRef.value.validate(async (valid) => {
    if (valid) {
      creating.value = true
      try {
        // Convert skills string to array
        const skills = skillsInput.value.split(',').map(skill => skill.trim()).filter(skill => skill)
        const taskData = { ...newTask, required_skills: skills }
        
        await api.post('/tasks/', taskData)
        ElMessage.success('Task created successfully!')
        showCreateDialog.value = false
        
        // Reset form
        Object.assign(newTask, {
          title: '',
          description: '',
          project: '',
          priority: 'MEDIUM',
          estimated_hours: 8,
          due_date: null,
          required_skills: []
        })
        skillsInput.value = ''
        
        fetchTasks()
      } catch (error) {
        console.error('Error creating task:', error)
        ElMessage.error('Failed to create task')
      } finally {
        creating.value = false
      }
    }
  })
}

const viewTask = (task) => {
  // TODO: Implement task detail view
  console.log('View task:', task)
}

onMounted(() => {
  fetchTasks()
  fetchProjects()
})
</script>

<style scoped>
.tasks {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.unassigned {
  color: #909399;
  font-style: italic;
}

.overdue {
  color: #f56c6c;
  font-weight: bold;
}
</style>
