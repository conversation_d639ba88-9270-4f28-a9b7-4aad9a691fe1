<template>
  <div class="team-management">
    <div class="page-header">
      <h1>Team Management</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        Add Team Member
      </el-button>
    </div>

    <!-- Team Members List -->
    <el-card>
      <el-table :data="teamMembers" v-loading="loading">
        <el-table-column prop="name" label="Name" min-width="150" />
        <el-table-column prop="ai_role_name" label="Role" width="150" />
        <el-table-column prop="specialization" label="Specialization" min-width="200" />
        <el-table-column prop="availability_hours" label="Availability" width="120">
          <template #default="{ row }">
            {{ row.availability_hours }}h/week
          </template>
        </el-table-column>
        <el-table-column prop="current_workload" label="Current Load" width="120">
          <template #default="{ row }">
            {{ row.current_workload }}h/week
          </template>
        </el-table-column>
        <el-table-column prop="workload_percentage" label="Utilization" width="150">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.workload_percentage" 
              :color="getWorkloadColor(row.workload_percentage)"
              :stroke-width="6"
            />
          </template>
        </el-table-column>
        <el-table-column prop="performance_rating" label="Rating" width="100">
          <template #default="{ row }">
            <el-rate v-model="row.performance_rating" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column label="Status" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? 'Active' : 'Inactive' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Create Team Member Dialog -->
    <el-dialog v-model="showCreateDialog" title="Add Team Member" width="600px">
      <el-form :model="newMember" :rules="memberRules" ref="memberFormRef">
        <el-form-item label="AI Role" prop="ai_role">
          <el-select v-model="newMember.ai_role" style="width: 100%">
            <el-option 
              v-for="role in aiRoles" 
              :key="role.id" 
              :label="role.name" 
              :value="role.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Name" prop="name">
          <el-input v-model="newMember.name" />
        </el-form-item>
        <el-form-item label="Specialization" prop="specialization">
          <el-input v-model="newMember.specialization" />
        </el-form-item>
        <el-form-item label="Skills" prop="skills">
          <el-input v-model="skillsInput" placeholder="Enter skills separated by commas" />
        </el-form-item>
        <el-form-item label="Availability (hours/week)" prop="availability_hours">
          <el-input-number v-model="newMember.availability_hours" :min="1" :max="60" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">Cancel</el-button>
        <el-button type="primary" @click="createMember" :loading="creating">Add Member</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '../services/api.js'

const teamMembers = ref([])
const aiRoles = ref([])
const loading = ref(false)
const showCreateDialog = ref(false)
const creating = ref(false)
const memberFormRef = ref()
const skillsInput = ref('')

const newMember = reactive({
  ai_role: '',
  name: '',
  specialization: '',
  skills: [],
  availability_hours: 40
})

const memberRules = {
  ai_role: [{ required: true, message: 'Please select an AI role', trigger: 'change' }],
  name: [{ required: true, message: 'Please enter member name', trigger: 'blur' }],
  specialization: [{ required: true, message: 'Please enter specialization', trigger: 'blur' }],
  availability_hours: [{ required: true, message: 'Please enter availability hours', trigger: 'blur' }]
}

const getWorkloadColor = (percentage) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const fetchTeamMembers = async () => {
  loading.value = true
  try {
    const response = await api.get('/team-members/')
    teamMembers.value = response.data.results
  } catch (error) {
    console.error('Error fetching team members:', error)
    ElMessage.error('Failed to fetch team members')
  } finally {
    loading.value = false
  }
}

const fetchAIRoles = async () => {
  try {
    const response = await api.get('/ai-roles/')
    aiRoles.value = response.data.results
  } catch (error) {
    console.error('Error fetching AI roles:', error)
  }
}

const createMember = async () => {
  if (!memberFormRef.value) return
  
  await memberFormRef.value.validate(async (valid) => {
    if (valid) {
      creating.value = true
      try {
        // Convert skills string to array
        const skills = skillsInput.value.split(',').map(skill => skill.trim()).filter(skill => skill)
        const memberData = { ...newMember, skills }
        
        await api.post('/team-members/', memberData)
        ElMessage.success('Team member added successfully!')
        showCreateDialog.value = false
        
        // Reset form
        Object.assign(newMember, {
          ai_role: '',
          name: '',
          specialization: '',
          skills: [],
          availability_hours: 40
        })
        skillsInput.value = ''
        
        fetchTeamMembers()
      } catch (error) {
        console.error('Error creating team member:', error)
        ElMessage.error('Failed to add team member')
      } finally {
        creating.value = false
      }
    }
  })
}

onMounted(() => {
  fetchTeamMembers()
  fetchAIRoles()
})
</script>

<style scoped>
.team-management {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}
</style>
