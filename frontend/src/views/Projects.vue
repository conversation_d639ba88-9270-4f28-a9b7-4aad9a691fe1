<template>
  <div class="projects">
    <div class="page-header">
      <h1>Projects</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        New Project
      </el-button>
    </div>

    <!-- Projects List -->
    <el-card>
      <el-table :data="projects" v-loading="loading">
        <el-table-column prop="title" label="Title" min-width="200" />
        <el-table-column prop="description" label="Description" min-width="300" />
        <el-table-column prop="status" label="Status" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="Priority" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress_percentage" label="Progress" width="120">
          <template #default="{ row }">
            <el-progress :percentage="row.progress_percentage" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="Created" width="120">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="viewProject(row.id)">View</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Create Project Dialog -->
    <el-dialog v-model="showCreateDialog" title="Create New Project" width="600px">
      <el-form :model="newProject" :rules="projectRules" ref="projectFormRef">
        <el-form-item label="Title" prop="title">
          <el-input v-model="newProject.title" />
        </el-form-item>
        <el-form-item label="Description" prop="description">
          <el-input v-model="newProject.description" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="Requirements" prop="requirements">
          <el-input v-model="newProject.requirements" type="textarea" rows="5" />
        </el-form-item>
        <el-form-item label="Priority" prop="priority">
          <el-select v-model="newProject.priority" style="width: 100%">
            <el-option label="Low" value="LOW" />
            <el-option label="Medium" value="MEDIUM" />
            <el-option label="High" value="HIGH" />
            <el-option label="Critical" value="CRITICAL" />
          </el-select>
        </el-form-item>
        <el-form-item label="Due Date">
          <el-date-picker v-model="newProject.due_date" type="date" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">Cancel</el-button>
        <el-button type="primary" @click="createProject" :loading="creating">Create</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '../services/api.js'

const router = useRouter()
const projects = ref([])
const loading = ref(false)
const showCreateDialog = ref(false)
const creating = ref(false)
const projectFormRef = ref()

const newProject = reactive({
  title: '',
  description: '',
  requirements: '',
  priority: 'MEDIUM',
  due_date: null
})

const projectRules = {
  title: [{ required: true, message: 'Please enter project title', trigger: 'blur' }],
  description: [{ required: true, message: 'Please enter project description', trigger: 'blur' }],
  requirements: [{ required: true, message: 'Please enter project requirements', trigger: 'blur' }],
  priority: [{ required: true, message: 'Please select priority', trigger: 'change' }]
}

const getStatusType = (status) => {
  const types = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'ON_HOLD': 'danger',
    'CANCELLED': 'info'
  }
  return types[status] || 'info'
}

const getPriorityType = (priority) => {
  const types = {
    'LOW': 'info',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
    'CRITICAL': 'danger'
  }
  return types[priority] || 'info'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const fetchProjects = async () => {
  loading.value = true
  try {
    const response = await api.get('/projects/')
    projects.value = response.data.results
  } catch (error) {
    console.error('Error fetching projects:', error)
    ElMessage.error('Failed to fetch projects')
  } finally {
    loading.value = false
  }
}

const createProject = async () => {
  if (!projectFormRef.value) return
  
  await projectFormRef.value.validate(async (valid) => {
    if (valid) {
      creating.value = true
      try {
        await api.post('/projects/', newProject)
        ElMessage.success('Project created successfully!')
        showCreateDialog.value = false
        Object.assign(newProject, {
          title: '',
          description: '',
          requirements: '',
          priority: 'MEDIUM',
          due_date: null
        })
        fetchProjects()
      } catch (error) {
        console.error('Error creating project:', error)
        ElMessage.error('Failed to create project')
      } finally {
        creating.value = false
      }
    }
  })
}

const viewProject = (projectId) => {
  router.push(`/projects/${projectId}`)
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.projects {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}
</style>
