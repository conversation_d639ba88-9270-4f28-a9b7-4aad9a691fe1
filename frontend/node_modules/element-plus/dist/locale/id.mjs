/*! Element Plus v2.10.3 */

var id = {
  name: "id",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "<PERSON><PERSON><PERSON>",
      clear: "Kosongkan"
    },
    datepicker: {
      now: "<PERSON><PERSON><PERSON>",
      today: "Hari ini",
      cancel: "<PERSON><PERSON>",
      clear: "Kosongkan",
      confirm: "Ya",
      selectDate: "<PERSON><PERSON><PERSON> tanggal",
      selectTime: "<PERSON>lih waktu",
      startDate: "Tanggal Mulai",
      startTime: "Waktu <PERSON>",
      endDate: "Tanggal Selesai",
      endTime: "<PERSON>ak<PERSON>",
      prevYear: "Tahun Sebelumnya",
      nextYear: "Tahun Selanjutnya",
      prevMonth: "Bulan Sebelumnya",
      nextMonth: "Bulan Selanjutnya",
      year: "<PERSON>hun",
      month1: "Januari",
      month2: "Februari",
      month3: "Maret",
      month4: "April",
      month5: "<PERSON>",
      month6: "<PERSON><PERSON>",
      month7: "<PERSON><PERSON>",
      month8: "<PERSON><PERSON><PERSON>",
      month9: "September",
      month10: "Ok<PERSON><PERSON>",
      month11: "November",
      month12: "Desember",
      week: "<PERSON><PERSON>",
      weeks: {
        sun: "Min",
        mon: "Sen",
        tue: "Se<PERSON>",
        wed: "Rab",
        thu: "Kam",
        fri: "Jum",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mei",
        jun: "Jun",
        jul: "Jul",
        aug: "Agu",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Memuat",
      noMatch: "Tidak ada data yg cocok",
      noData: "Tidak ada data",
      placeholder: "Pilih"
    },
    mention: {
      loading: "Memuat"
    },
    cascader: {
      noMatch: "Tidak ada data yg cocok",
      loading: "Memuat",
      placeholder: "Pilih",
      noData: "Tidak ada data"
    },
    pagination: {
      goto: "Pergi ke",
      pagesize: "/halaman",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages",
      deprecationWarning: "Penggunaan yang tidak akan digunakan lagi terdeteksi, silakan lihat dokumentasi el-pagination untuk lebih jelasnya"
    },
    messagebox: {
      title: "Pesan",
      confirm: "Ya",
      cancel: "Batal",
      error: "Masukan ilegal"
    },
    upload: {
      deleteTip: "Tekan hapus untuk melanjutkan",
      delete: "Hapus",
      preview: "Pratinjau",
      continue: "Lanjutkan"
    },
    table: {
      emptyText: "Tidak ada data",
      confirmFilter: "Konfirmasi",
      resetFilter: "Atur ulang",
      clearFilter: "Semua",
      sumText: "Jumlah"
    },
    tree: {
      emptyText: "Tidak ada data"
    },
    transfer: {
      noMatch: "Tidak ada data yg cocok",
      noData: "Tidak ada data",
      titles: ["Daftar 1", "Daftar 2"],
      filterPlaceholder: "Masukan kata kunci",
      noCheckedFormat: "{total} item",
      hasCheckedFormat: "{checked}/{total} terpilih"
    },
    image: {
      error: "GAGAL"
    },
    pageHeader: {
      title: "Kembali"
    },
    popconfirm: {
      confirmButtonText: "Ya",
      cancelButtonText: "Tidak"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { id as default };
