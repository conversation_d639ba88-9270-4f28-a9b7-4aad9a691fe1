/*! Element Plus v2.10.3 */

var hyAm = {
  name: "hy-am",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "\u053C\u0561\u0582",
      clear: "\u0544\u0561\u0584\u0580\u0565\u056C"
    },
    datepicker: {
      now: "\u0540\u056B\u0574\u0561",
      today: "\u0531\u0575\u057D\u0585\u0580",
      cancel: "\u0549\u0565\u0572\u0561\u0580\u056F\u0565\u056C",
      clear: "\u0544\u0561\u0584\u0580\u0565\u056C",
      confirm: "\u053C\u0561\u0582",
      selectDate: "\u0538\u0576\u057F\u0580\u0565\u0584 \u0561\u0574\u057D\u0561\u0569\u056B\u0582\u0568",
      selectTime: "\u0538\u0576\u057F\u0580\u0565\u0584 \u056A\u0561\u0574\u0561\u0576\u0561\u056F\u0568",
      startDate: "\u054D\u056F\u0566\u0562. \u0561\u0574\u057D\u0561\u0569\u056B\u0582\u0568",
      startTime: "\u054D\u056F\u0566\u0562. \u056A\u0561\u0574\u0561\u0576\u0561\u056F\u0568",
      endDate: "\u054E\u0565\u0580\u057B. \u0561\u0574\u057D\u0561\u0569\u056B\u057E\u0568",
      endTime: "\u054E\u0565\u0580\u057B. \u056A\u0561\u0574\u0561\u0576\u0561\u056F\u0568",
      prevYear: "\u0546\u0561\u056D\u0578\u0580\u0564 \u057F\u0561\u0580\u056B",
      nextYear: "\u0545\u0561\u057B\u0578\u0580\u0564 \u057F\u0561\u0580\u056B",
      prevMonth: "\u0546\u0561\u056D\u0578\u0580\u0564 \u0561\u0574\u056B\u057D",
      nextMonth: "\u0545\u0561\u057B\u0578\u0580\u0564 \u0561\u0574\u056B\u057D",
      year: "\u054F\u0561\u0580\u056B",
      month1: "\u0545\u0578\u0582\u0576\u0578\u0582\u0561\u0580",
      month2: "\u0553\u0565\u057F\u0580\u0578\u0582\u0561\u0580",
      month3: "\u0544\u0561\u0580\u057F",
      month4: "\u0531\u057A\u0580\u056B\u056C",
      month5: "\u0544\u0561\u0575\u056B\u057D",
      month6: "\u0545\u0578\u0582\u0576\u056B\u057D",
      month7: "\u0545\u0578\u0582\u056C\u056B\u057D",
      month8: "\u0555\u0563\u0578\u057D\u057F\u0578\u057D",
      month9: "\u054D\u0565\u057A\u057F\u0565\u0574\u0562\u0565\u0580",
      month10: "\u0545\u0578\u056F\u057F\u0565\u0574\u0562\u0565\u0580",
      month11: "\u0546\u0578\u0575\u0565\u0574\u0562\u0565\u0580",
      month12: "\u0534\u0565\u056F\u057F\u0565\u0574\u0562\u0565\u0580",
      week: "\u0547\u0561\u0562\u0561\u0569",
      weeks: {
        sun: "\u053F\u056B\u0580",
        mon: "\u0535\u0580\u056F",
        tue: "\u0535\u0580",
        wed: "\u0549\u0578\u0580",
        thu: "\u0540\u056B\u0576\u0563",
        fri: "\u0548\u0582\u0580\u0562",
        sat: "\u0547\u0561\u0562"
      },
      months: {
        jan: "\u0545\u0578\u0582\u0576\u057E",
        feb: "\u0553\u0565\u057F",
        mar: "\u0544\u0561\u0580",
        apr: "\u0531\u057A\u0580",
        may: "\u0544\u0561\u0575",
        jun: "\u0545\u0578\u0582\u0576",
        jul: "\u0545\u0578\u0582\u056C",
        aug: "\u0555\u0563",
        sep: "\u054D\u0565\u057A\u057F",
        oct: "\u0545\u0578\u056F",
        nov: "\u0546\u0578\u0575",
        dec: "\u0534\u0565\u056F"
      }
    },
    select: {
      loading: "\u0532\u0565\u057C\u0576\u0578\u0582\u0574",
      noMatch: "\u0540\u0561\u0574\u0561\u057A\u0561\u057F\u0561\u057D\u056D\u0561\u0576 \u057F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      noData: "\u054F\u057E\u0575\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      placeholder: "\u0538\u0576\u057F\u0580\u0565\u056C"
    },
    mention: {
      loading: "\u0532\u0565\u057C\u0576\u0578\u0582\u0574"
    },
    cascader: {
      noMatch: "\u0540\u0561\u0574\u0561\u057A\u0561\u057F\u0561\u057D\u056D\u0561\u0576 \u057F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      loading: "\u0532\u0565\u057C\u0576\u0578\u0582\u0574",
      placeholder: "\u0538\u0576\u057F\u0580\u0565\u056C",
      noData: "\u054F\u057E\u0575\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576"
    },
    pagination: {
      goto: "\u0531\u0576\u0581\u0576\u0565\u056C",
      pagesize: " \u0567\u057B\u0578\u0582\u0574",
      total: "\u0538\u0576\u0564\u0561\u0574\u0565\u0576\u0568 {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "\u0540\u0561\u0572\u0578\u0580\u0564\u0561\u0563\u0580\u0578\u0582\u0569\u056B\u0582\u0576",
      confirm: "\u053C\u0561\u0582",
      cancel: "\u0549\u0565\u0572\u0561\u0580\u056F\u0565\u056C",
      error: "\u0531\u0576\u057E\u0561\u0582\u0565\u0580 \u057F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580\u056B \u0574\u0578\u0582\u057F\u0584"
    },
    upload: {
      deleteTip: "\u054D\u0565\u0572\u0574\u0565\u0584 [\u054B\u0576\u057B\u0565\u056C] \u057B\u0576\u057B\u0565\u056C\u0578\u0582 \u0570\u0561\u0574\u0561\u0580",
      delete: "\u054B\u0576\u057B\u0565\u056C",
      preview: "\u0546\u0561\u056D\u0561\u0564\u056B\u057F\u0578\u0582\u0574",
      continue: "\u0547\u0561\u0580\u0578\u0582\u0576\u0561\u056F\u0565\u056C"
    },
    table: {
      emptyText: "\u054F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      confirmFilter: "\u0545\u0561\u057D\u057F\u0561\u057F\u0565\u056C",
      resetFilter: "\u054E\u0565\u0580\u0561\u0563\u0578\u0580\u056E\u0561\u0580\u056F\u0565\u056C",
      clearFilter: "\u0532\u0578\u056C\u0578\u0580\u0568",
      sumText: "\u0533\u0578\u0582\u0574\u0561\u0580\u0568"
    },
    tree: {
      emptyText: "\u054F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576"
    },
    transfer: {
      noMatch: "\u0540\u0561\u0574\u0561\u057A\u0561\u057F\u0561\u057D\u056D\u0561\u0576 \u057F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      noData: "\u054F\u0578\u0582\u0565\u0561\u056C\u0576\u0565\u0580 \u0579\u056F\u0561\u0576",
      titles: ["\u0551\u0578\u0582\u0581\u0561\u056F 1", "\u0551\u0578\u0582\u0581\u0561\u056F 2"],
      filterPlaceholder: "\u0544\u0578\u0582\u057F\u0584\u0561\u0563\u0580\u0565\u0584 \u0562\u0561\u0576\u0561\u056C\u056B \u0562\u0561\u057C",
      noCheckedFormat: "{total} \u0574\u056B\u0561\u0582\u0578\u0580",
      hasCheckedFormat: "{checked}/{total} \u0568\u0576\u057F\u0580\u0578\u0582\u0561\u056E \u0567"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { hyAm as default };
