/*! Element Plus v2.10.3 */

var nbNo = {
  name: "nb-no",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "T\xF8m"
    },
    datepicker: {
      now: "N\xE5",
      today: "I dag",
      cancel: "Avbryt",
      clear: "T\xF8m",
      confirm: "OK",
      selectDate: "Velg dato",
      selectTime: "Velg tidspunkt",
      startDate: "Startdato",
      startTime: "Starttidspunkt",
      endDate: "Sluttdato",
      endTime: "Sluttidspunkt",
      prevYear: "I fjor",
      nextYear: "Neste \xE5r",
      prevMonth: "Forrige M\xE5ned",
      nextMonth: "Neste M\xE5ned",
      year: "",
      month1: "Januar",
      month2: "Februar",
      month3: "Mars",
      month4: "April",
      month5: "Mai",
      month6: "Juni",
      month7: "Juli",
      month8: "August",
      month9: "September",
      month10: "Oktober",
      month11: "November",
      month12: "Desember",
      week: "uke",
      weeks: {
        sun: "S\xF8n",
        mon: "Man",
        tue: "Tir",
        wed: "Ons",
        thu: "Tor",
        fri: "Fre",
        sat: "L\xF8r"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Laster",
      noMatch: "Ingen samsvarende resulater",
      noData: "Ingen resulater",
      placeholder: "Velg"
    },
    mention: {
      loading: "Laster"
    },
    cascader: {
      noMatch: "Ingen samsvarende resultater",
      loading: "Laster",
      placeholder: "Velg",
      noData: "Ingen resultater"
    },
    pagination: {
      goto: "G\xE5 til",
      pagesize: "/side",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "OK",
      cancel: "Avbryt",
      error: "Ugyldig input"
    },
    upload: {
      deleteTip: "trykk p\xE5 x for \xE5 slette",
      delete: "Slett",
      preview: "Forh\xE5ndsvisning",
      continue: "Fortsett"
    },
    table: {
      emptyText: "Ingen Data",
      confirmFilter: "Bekreft",
      resetFilter: "Tilbakestill",
      clearFilter: "Alle",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Ingen Data"
    },
    transfer: {
      noMatch: "Ingen samsvarende data",
      noData: "Ingen data",
      titles: ["Liste 1", "Liste 2"],
      filterPlaceholder: "Skriv inn n\xF8kkelord",
      noCheckedFormat: "{total} gjenstander",
      hasCheckedFormat: "{checked}/{total} valgt"
    },
    image: {
      error: "FEILET"
    },
    pageHeader: {
      title: "Tilbake"
    },
    popconfirm: {
      confirmButtonText: "Ja",
      cancelButtonText: "Nei"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { nbNo as default };
