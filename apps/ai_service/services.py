import openai
import json
from django.conf import settings
from django.utils import timezone
from typing import Dict, List, Any
from .models import AI<PERSON><PERSON><PERSON><PERSON>emplate, AIDecision
from apps.team.models import TeamMember, AIRole


class AIService:
    """
    Main AI service class for handling OpenAI API interactions and AI-powered features.
    """
    
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )
        self.model = settings.OPENAI_MODEL
    
    def analyze_requirements(self, requirements: str, project) -> Dict[str, Any]:
        """
        Analyze project requirements and provide insights, complexity assessment, and recommendations.
        """
        system_prompt = self._get_prompt_template('REQUIREMENT_REVIEW', 'Product Manager')
        
        user_prompt = f"""
        Please analyze the following project requirements and provide a comprehensive assessment:

        Project Title: {project.title}
        Requirements:
        {requirements}

        Please provide your analysis in the following JSON format:
        {{
            "complexity_score": <1-10 scale>,
            "estimated_duration": "<duration in days>",
            "required_skills": ["skill1", "skill2", ...],
            "recommended_team": [
                {{"role": "role_name", "allocation": 0.5, "justification": "reason"}},
                ...
            ],
            "risk_assessment": {{
                "technical_risks": ["risk1", "risk2", ...],
                "timeline_risks": ["risk1", "risk2", ...],
                "resource_risks": ["risk1", "risk2", ...]
            }},
            "suggested_tasks": [
                {{
                    "title": "task_title",
                    "category": "category",
                    "estimated_hours": 20,
                    "priority": "HIGH|MEDIUM|LOW",
                    "reasoning": "why this task is needed"
                }},
                ...
            ],
            "confidence_score": 0.85
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                analysis = json.loads(content)
            except json.JSONDecodeError:
                # If JSON parsing fails, create a structured response
                analysis = {
                    "complexity_score": 7,
                    "estimated_duration": "30 days",
                    "required_skills": ["Software Development", "Project Management"],
                    "recommended_team": [
                        {"role": "Product Manager", "allocation": 0.5, "justification": "Project oversight"},
                        {"role": "Developer", "allocation": 1.0, "justification": "Implementation"}
                    ],
                    "risk_assessment": {
                        "technical_risks": ["Complex requirements"],
                        "timeline_risks": ["Scope creep"],
                        "resource_risks": ["Limited team capacity"]
                    },
                    "suggested_tasks": [
                        {
                            "title": "Requirements Analysis",
                            "category": "Planning",
                            "estimated_hours": 16,
                            "priority": "HIGH",
                            "reasoning": "Foundation for project success"
                        }
                    ],
                    "confidence_score": 0.7,
                    "raw_response": content
                }
            
            # Log the AI decision
            AIDecision.objects.create(
                decision_type='REQUIREMENT_ANALYSIS',
                decision_data=analysis,
                reasoning=f"Analyzed requirements for project: {project.title}",
                confidence_score=analysis.get('confidence_score', 0.7)
            )
            
            return analysis
            
        except Exception as e:
            # Fallback analysis if AI service fails
            return {
                "complexity_score": 5,
                "estimated_duration": "20 days",
                "required_skills": ["Software Development"],
                "recommended_team": [
                    {"role": "Developer", "allocation": 1.0, "justification": "Implementation"}
                ],
                "risk_assessment": {
                    "technical_risks": ["Unknown complexity"],
                    "timeline_risks": ["Estimation uncertainty"],
                    "resource_risks": ["AI service unavailable"]
                },
                "suggested_tasks": [
                    {
                        "title": "Project Planning",
                        "category": "Planning",
                        "estimated_hours": 8,
                        "priority": "HIGH",
                        "reasoning": "Initial project setup"
                    }
                ],
                "confidence_score": 0.3,
                "error": str(e)
            }
    
    def generate_tasks(self, project, focus_area: str = 'general') -> Dict[str, Any]:
        """
        Generate tasks for a project based on requirements and focus area.
        """
        system_prompt = self._get_prompt_template('TASK_ANALYSIS', 'Product Manager')
        
        user_prompt = f"""
        Generate detailed tasks for the following project with focus on {focus_area}:

        Project: {project.title}
        Description: {project.description}
        Requirements: {project.requirements}
        Current Status: {project.status}
        Priority: {project.priority}

        Please generate 5-10 specific, actionable tasks in the following JSON format:
        {{
            "tasks": [
                {{
                    "title": "task_title",
                    "description": "detailed_description",
                    "estimated_hours": 8,
                    "priority": "HIGH|MEDIUM|LOW",
                    "complexity_score": 5,
                    "required_skills": ["skill1", "skill2"],
                    "reasoning": "why this task is important"
                }},
                ...
            ],
            "reasoning": "overall reasoning for task selection and prioritization"
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )
            
            content = response.choices[0].message.content
            
            try:
                tasks_data = json.loads(content)
            except json.JSONDecodeError:
                # Fallback task generation
                tasks_data = {
                    "tasks": [
                        {
                            "title": f"Implement {focus_area} functionality",
                            "description": f"Develop and implement {focus_area} features for the project",
                            "estimated_hours": 16,
                            "priority": "HIGH",
                            "complexity_score": 6,
                            "required_skills": ["Software Development"],
                            "reasoning": "Core functionality implementation"
                        },
                        {
                            "title": f"Test {focus_area} features",
                            "description": f"Create and execute tests for {focus_area} functionality",
                            "estimated_hours": 8,
                            "priority": "MEDIUM",
                            "complexity_score": 4,
                            "required_skills": ["Testing", "Quality Assurance"],
                            "reasoning": "Ensure quality and reliability"
                        }
                    ],
                    "reasoning": f"Generated basic tasks for {focus_area} development",
                    "raw_response": content
                }
            
            # Log the AI decision
            AIDecision.objects.create(
                decision_type='TASK_GENERATION',
                decision_data=tasks_data,
                reasoning=f"Generated tasks for project: {project.title}, focus: {focus_area}",
                confidence_score=0.8
            )
            
            return tasks_data
            
        except Exception as e:
            # Fallback task generation
            return {
                "tasks": [
                    {
                        "title": "Project Setup",
                        "description": "Initial project setup and configuration",
                        "estimated_hours": 8,
                        "priority": "HIGH",
                        "complexity_score": 3,
                        "required_skills": ["Project Management"],
                        "reasoning": "Foundation for project development"
                    }
                ],
                "reasoning": "Fallback task generation due to AI service error",
                "error": str(e)
            }
    
    def evaluate_team_capacity(self, project) -> Dict[str, Any]:
        """
        Evaluate current team capacity and suggest headcount changes.
        """
        team_members = project.projectteammember_set.filter(is_active=True)
        
        # Calculate current capacity
        total_capacity = sum(tm.team_member.availability_hours * (tm.allocation_percentage / 100) 
                           for tm in team_members)
        total_workload = sum(tm.team_member.current_workload for tm in team_members)
        
        # Estimate project workload from tasks
        project_tasks = project.task_set.filter(status__in=['TODO', 'IN_PROGRESS'])
        estimated_workload = sum(task.estimated_hours for task in project_tasks)
        
        capacity_analysis = {
            "current_team_size": team_members.count(),
            "total_capacity_hours": total_capacity,
            "current_workload_hours": total_workload,
            "project_estimated_hours": estimated_workload,
            "capacity_utilization": (total_workload / total_capacity * 100) if total_capacity > 0 else 0,
            "capacity_gap": max(0, estimated_workload - (total_capacity - total_workload)),
            "recommendations": []
        }
        
        # Generate recommendations
        if capacity_analysis["capacity_utilization"] > 90:
            capacity_analysis["recommendations"].append({
                "type": "OVERLOADED",
                "message": "Team is overloaded. Consider adding resources or reducing scope.",
                "urgency": "HIGH"
            })
        elif capacity_analysis["capacity_gap"] > 40:
            capacity_analysis["recommendations"].append({
                "type": "INSUFFICIENT_CAPACITY",
                "message": "Insufficient capacity for project requirements. Additional team members needed.",
                "urgency": "MEDIUM"
            })
        
        return capacity_analysis
    
    def _get_prompt_template(self, template_type: str, role_specific: str = None) -> str:
        """
        Get AI prompt template for specific use case.
        """
        try:
            template = AIPromptTemplate.objects.filter(
                template_type=template_type,
                role_specific=role_specific,
                is_active=True
            ).first()
            
            if template:
                return template.template_content
        except:
            pass
        
        # Default prompts
        default_prompts = {
            'REQUIREMENT_REVIEW': """You are an experienced Product Manager with expertise in software development, 
            user research, and project planning. You excel at analyzing requirements and breaking them down into 
            actionable insights. Provide detailed, practical analysis with realistic estimates.""",
            
            'TASK_ANALYSIS': """You are a skilled Product Manager who excels at breaking down complex projects 
            into specific, actionable tasks. Create detailed tasks that are clear, measurable, and properly estimated. 
            Consider dependencies and prioritize based on business value and technical requirements."""
        }
        
        return default_prompts.get(template_type, "You are a helpful AI assistant.")

    def ai_pm_project_review(self, project, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI Product Manager reviews project status and provides recommendations.
        """
        system_prompt = """You are an experienced AI Product Manager. Review the project status,
        analyze progress, identify risks, and provide actionable recommendations. Be specific and practical."""

        # Get project data
        tasks = project.task_set.all()
        team_members = project.projectteammember_set.filter(is_active=True)

        project_summary = {
            "title": project.title,
            "status": project.status,
            "priority": project.priority,
            "progress": project.progress_percentage,
            "total_tasks": tasks.count(),
            "completed_tasks": tasks.filter(status='COMPLETED').count(),
            "team_size": team_members.count(),
            "days_since_creation": (timezone.now().date() - project.created_at.date()).days
        }

        user_prompt = f"""
        Please review this project and provide recommendations:

        Project Summary: {json.dumps(project_summary, indent=2)}
        Context: {json.dumps(context, indent=2)}

        Provide your review in JSON format:
        {{
            "overall_assessment": "assessment_text",
            "progress_status": "ON_TRACK|BEHIND|AHEAD",
            "key_achievements": ["achievement1", "achievement2"],
            "identified_risks": [
                {{"risk": "risk_description", "severity": "HIGH|MEDIUM|LOW", "mitigation": "suggested_action"}}
            ],
            "recommendations": [
                {{"action": "action_description", "priority": "HIGH|MEDIUM|LOW", "timeline": "timeframe"}}
            ],
            "next_steps": ["step1", "step2"],
            "confidence_score": 0.85
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=1000
            )

            content = response.choices[0].message.content

            try:
                review_data = json.loads(content)
            except json.JSONDecodeError:
                review_data = {
                    "overall_assessment": "Project review completed with limited AI analysis",
                    "progress_status": "ON_TRACK",
                    "key_achievements": ["Project is active"],
                    "identified_risks": [],
                    "recommendations": [{"action": "Continue current approach", "priority": "MEDIUM", "timeline": "ongoing"}],
                    "next_steps": ["Monitor progress"],
                    "confidence_score": 0.5,
                    "raw_response": content
                }

            # Log the AI decision
            AIDecision.objects.create(
                decision_type='PROJECT_REVIEW',
                decision_data=review_data,
                reasoning=f"AI PM project review for: {project.title}",
                confidence_score=review_data.get('confidence_score', 0.7)
            )

            return review_data

        except Exception as e:
            return {
                "overall_assessment": "Unable to complete AI review",
                "progress_status": "UNKNOWN",
                "key_achievements": [],
                "identified_risks": [{"risk": "AI service unavailable", "severity": "LOW", "mitigation": "Manual review needed"}],
                "recommendations": [{"action": "Conduct manual project review", "priority": "MEDIUM", "timeline": "this week"}],
                "next_steps": ["Manual assessment"],
                "confidence_score": 0.1,
                "error": str(e)
            }

    def ai_pm_prioritize_tasks(self, project, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI Product Manager prioritizes tasks based on business value and dependencies.
        """
        tasks = project.task_set.filter(status__in=['TODO', 'IN_PROGRESS'])

        if not tasks.exists():
            return {
                "message": "No tasks to prioritize",
                "prioritized_tasks": [],
                "reasoning": "Project has no active tasks"
            }

        task_data = [
            {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "current_priority": task.priority,
                "status": task.status,
                "estimated_hours": float(task.estimated_hours),
                "complexity_score": task.complexity_score,
                "required_skills": task.required_skills
            } for task in tasks
        ]

        system_prompt = """You are an AI Product Manager expert at task prioritization.
        Analyze tasks and prioritize them based on business value, dependencies, risk, and resource availability."""

        user_prompt = f"""
        Prioritize these tasks for maximum business value and efficient execution:

        Tasks: {json.dumps(task_data, indent=2)}
        Project Priority: {project.priority}
        Context: {json.dumps(context, indent=2)}

        Provide prioritization in JSON format:
        {{
            "prioritized_tasks": [
                {{
                    "task_id": "task_id",
                    "new_priority": "HIGH|MEDIUM|LOW",
                    "rank": 1,
                    "reasoning": "why this priority"
                }}
            ],
            "overall_strategy": "prioritization_strategy_explanation",
            "confidence_score": 0.85
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.6,
                max_tokens=800
            )

            content = response.choices[0].message.content

            try:
                priority_data = json.loads(content)
            except json.JSONDecodeError:
                # Fallback prioritization
                priority_data = {
                    "prioritized_tasks": [
                        {
                            "task_id": str(task.id),
                            "new_priority": task.priority,
                            "rank": i + 1,
                            "reasoning": "Maintained current priority"
                        } for i, task in enumerate(tasks)
                    ],
                    "overall_strategy": "Maintained existing priorities due to AI parsing error",
                    "confidence_score": 0.3,
                    "raw_response": content
                }

            return priority_data

        except Exception as e:
            return {
                "prioritized_tasks": [],
                "overall_strategy": "Unable to prioritize due to AI service error",
                "confidence_score": 0.1,
                "error": str(e)
            }

    def ai_pm_resource_planning(self, project, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI Product Manager evaluates resource needs and generates headcount requests.
        """
        capacity_analysis = self.evaluate_team_capacity(project)

        system_prompt = """You are an AI Product Manager responsible for resource planning.
        Analyze team capacity and project requirements to determine if additional resources are needed."""

        user_prompt = f"""
        Analyze resource needs for this project:

        Capacity Analysis: {json.dumps(capacity_analysis, indent=2)}
        Project Priority: {project.priority}
        Project Status: {project.status}
        Context: {json.dumps(context, indent=2)}

        Provide resource planning recommendations in JSON format:
        {{
            "resource_assessment": "SUFFICIENT|INSUFFICIENT|OVERLOADED",
            "recommended_actions": [
                {{
                    "action_type": "HIRE|REALLOCATE|REDUCE_SCOPE",
                    "role_needed": "role_name",
                    "justification": "detailed_reasoning",
                    "urgency": "HIGH|MEDIUM|LOW",
                    "estimated_cost": 50000
                }}
            ],
            "timeline_impact": "impact_description",
            "confidence_score": 0.85
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.6,
                max_tokens=800
            )

            content = response.choices[0].message.content

            try:
                resource_data = json.loads(content)
            except json.JSONDecodeError:
                resource_data = {
                    "resource_assessment": "SUFFICIENT",
                    "recommended_actions": [],
                    "timeline_impact": "No immediate impact identified",
                    "confidence_score": 0.3,
                    "raw_response": content
                }

            return resource_data

        except Exception as e:
            return {
                "resource_assessment": "UNKNOWN",
                "recommended_actions": [],
                "timeline_impact": "Unable to assess due to AI service error",
                "confidence_score": 0.1,
                "error": str(e)
            }

    def ai_pm_progress_assessment(self, project, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI Product Manager assesses project progress and provides status updates.
        """
        tasks = project.task_set.all()
        completed_tasks = tasks.filter(status='COMPLETED')

        progress_data = {
            "total_tasks": tasks.count(),
            "completed_tasks": completed_tasks.count(),
            "progress_percentage": project.progress_percentage,
            "status": project.status,
            "priority": project.priority,
            "days_active": (timezone.now().date() - project.created_at.date()).days
        }

        system_prompt = """You are an AI Product Manager tracking project progress.
        Analyze current status and provide insights on project health and trajectory."""

        user_prompt = f"""
        Assess the progress of this project:

        Progress Data: {json.dumps(progress_data, indent=2)}
        Context: {json.dumps(context, indent=2)}

        Provide progress assessment in JSON format:
        {{
            "progress_status": "ON_TRACK|BEHIND|AHEAD|AT_RISK",
            "health_score": 85,
            "key_metrics": {{
                "velocity": "tasks_per_week",
                "quality": "quality_assessment",
                "team_efficiency": "efficiency_rating"
            }},
            "blockers": ["blocker1", "blocker2"],
            "achievements": ["achievement1", "achievement2"],
            "next_milestones": [
                {{"milestone": "milestone_name", "target_date": "date", "confidence": "HIGH|MEDIUM|LOW"}}
            ],
            "recommendations": ["recommendation1", "recommendation2"],
            "confidence_score": 0.85
        }}
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.6,
                max_tokens=800
            )

            content = response.choices[0].message.content

            try:
                assessment_data = json.loads(content)
            except json.JSONDecodeError:
                assessment_data = {
                    "progress_status": "ON_TRACK",
                    "health_score": 70,
                    "key_metrics": {
                        "velocity": "Unknown",
                        "quality": "Unknown",
                        "team_efficiency": "Unknown"
                    },
                    "blockers": [],
                    "achievements": ["Project is active"],
                    "next_milestones": [],
                    "recommendations": ["Continue monitoring progress"],
                    "confidence_score": 0.3,
                    "raw_response": content
                }

            return assessment_data

        except Exception as e:
            return {
                "progress_status": "UNKNOWN",
                "health_score": 50,
                "key_metrics": {"velocity": "Error", "quality": "Error", "team_efficiency": "Error"},
                "blockers": ["AI service unavailable"],
                "achievements": [],
                "next_milestones": [],
                "recommendations": ["Manual progress review needed"],
                "confidence_score": 0.1,
                "error": str(e)
            }
