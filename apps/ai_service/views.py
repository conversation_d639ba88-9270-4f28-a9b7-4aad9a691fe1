from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.conf import settings
import openai
import json
from .models import AIConversation, AIDecision, AIPromptTemplate
from .services import AIService
from apps.projects.models import Project
from apps.tasks.models import Task


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def analyze_requirements(request):
    """
    Analyze project requirements using AI and generate insights.
    """
    project_id = request.data.get('project_id')
    requirements = request.data.get('requirements')

    if not project_id or not requirements:
        return Response(
            {'error': 'project_id and requirements are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        project = Project.objects.get(id=project_id, owner=request.user)
    except Project.DoesNotExist:
        return Response(
            {'error': 'Project not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        ai_service = AIService()
        analysis = ai_service.analyze_requirements(requirements, project)

        # Save the analysis to the project
        project.ai_analysis = analysis
        project.save()

        # Create conversation record
        AIConversation.objects.create(
            project=project,
            conversation_type='REQUIREMENT_ANALYSIS',
            context_data={'requirements': requirements},
            messages=[
                {'role': 'user', 'content': requirements},
                {'role': 'assistant', 'content': json.dumps(analysis)}
            ]
        )

        return Response({
            'analysis': analysis,
            'confidence_score': analysis.get('confidence_score', 0.8)
        })

    except Exception as e:
        return Response(
            {'error': f'AI analysis failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def generate_tasks(request):
    """
    Generate tasks for a project using AI.
    """
    project_id = request.data.get('project_id')
    focus_area = request.data.get('focus_area', 'general')

    if not project_id:
        return Response(
            {'error': 'project_id is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        project = Project.objects.get(id=project_id, owner=request.user)
    except Project.DoesNotExist:
        return Response(
            {'error': 'Project not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        ai_service = AIService()
        generated_tasks = ai_service.generate_tasks(project, focus_area)

        # Create tasks in the database
        created_tasks = []
        for task_data in generated_tasks.get('tasks', []):
            task = Task.objects.create(
                project=project,
                title=task_data['title'],
                description=task_data['description'],
                estimated_hours=task_data.get('estimated_hours', 8),
                priority=task_data.get('priority', 'MEDIUM'),
                complexity_score=task_data.get('complexity_score', 5),
                required_skills=task_data.get('required_skills', []),
                ai_generated=True,
                ai_reasoning=task_data.get('reasoning', '')
            )
            created_tasks.append(task)

        # Create conversation record
        AIConversation.objects.create(
            project=project,
            conversation_type='TASK_GENERATION',
            context_data={'focus_area': focus_area},
            messages=[
                {'role': 'user', 'content': f'Generate tasks for {focus_area}'},
                {'role': 'assistant', 'content': json.dumps(generated_tasks)}
            ]
        )

        return Response({
            'generated_tasks': [
                {
                    'id': task.id,
                    'title': task.title,
                    'description': task.description,
                    'estimated_hours': task.estimated_hours,
                    'priority': task.priority,
                    'reasoning': task.ai_reasoning
                } for task in created_tasks
            ],
            'total_estimated_hours': sum(task.estimated_hours for task in created_tasks),
            'ai_reasoning': generated_tasks.get('reasoning', '')
        })

    except Exception as e:
        return Response(
            {'error': f'Task generation failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def evaluate_team_capacity(request):
    """
    Evaluate team capacity and generate headcount recommendations.
    """
    project_id = request.data.get('project_id')

    if not project_id:
        return Response(
            {'error': 'project_id is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        project = Project.objects.get(id=project_id, owner=request.user)
    except Project.DoesNotExist:
        return Response(
            {'error': 'Project not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        ai_service = AIService()
        capacity_analysis = ai_service.evaluate_team_capacity(project)

        return Response(capacity_analysis)

    except Exception as e:
        return Response(
            {'error': f'Capacity evaluation failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def ai_product_manager_action(request):
    """
    Simulate AI Product Manager making decisions and taking actions.
    """
    action_type = request.data.get('action_type')
    project_id = request.data.get('project_id')
    context = request.data.get('context', {})

    if not action_type or not project_id:
        return Response(
            {'error': 'action_type and project_id are required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        project = Project.objects.get(id=project_id, owner=request.user)
    except Project.DoesNotExist:
        return Response(
            {'error': 'Project not found'},
            status=status.HTTP_404_NOT_FOUND
        )

    try:
        ai_service = AIService()

        if action_type == 'project_review':
            result = ai_service.ai_pm_project_review(project, context)
        elif action_type == 'task_prioritization':
            result = ai_service.ai_pm_prioritize_tasks(project, context)
        elif action_type == 'resource_planning':
            result = ai_service.ai_pm_resource_planning(project, context)
        elif action_type == 'progress_assessment':
            result = ai_service.ai_pm_progress_assessment(project, context)
        else:
            return Response(
                {'error': f'Unknown action_type: {action_type}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(result)

    except Exception as e:
        return Response(
            {'error': f'AI PM action failed: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
