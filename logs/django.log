INFO 2025-07-06 10:44:47,764 autoreload 54204 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 10:49:29,107 autoreload 54773 8400735296 Watching for file changes with StatReloader
WARNING 2025-07-06 10:49:52,798 log 54773 6192869376 Unauthorized: /api/v1/projects/
WARNING 2025-07-06 10:49:52,798 basehttp 54773 6192869376 "GET /api/v1/projects/ HTTP/1.1" 401 58
INFO 2025-07-06 10:50:50,177 autoreload 54960 8400735296 Watching for file changes with StatReloader
ERROR 2025-07-06 10:51:08,057 log 54960 6191984640 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/apps/authentication/views.py", line 40, in login_view
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
ERROR 2025-07-06 10:51:08,058 basehttp 54960 6191984640 "POST /api/v1/auth/login/ HTTP/1.1" 500 97549
ERROR 2025-07-06 10:51:48,851 log 54960 6191984640 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/django/views/generic/base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/.venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/ai-partner/apps/authentication/views.py", line 40, in login_view
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
ERROR 2025-07-06 10:51:48,852 basehttp 54960 6191984640 "POST /api/v1/auth/login/ HTTP/1.1" 500 97549
INFO 2025-07-06 10:52:38,385 autoreload 55250 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 10:53:24,359 basehttp 55250 6192820224 "POST /api/v1/auth/login/ HTTP/1.1" 200 342
INFO 2025-07-06 10:53:32,944 basehttp 55250 6192820224 "GET /api/v1/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 10:53:41,039 basehttp 55250 6192820224 "GET /api/v1/team-members/ HTTP/1.1" 200 997
INFO 2025-07-06 11:32:35,366 autoreload 62265 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:33:19,434 basehttp 62265 6159593472 "GET /api/v1/projects/ HTTP/1.1" 200 702
INFO 2025-07-06 11:33:32,108 basehttp 62265 6159593472 "POST /api/v1/ai/evaluate-team-capacity/ HTTP/1.1" 200 166
INFO 2025-07-06 11:35:47,524 autoreload 62928 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:36:25,195 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:36:29,650 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:39:22,068 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:39:57,230 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:40:03,954 basehttp 62928 6196621312 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
WARNING 2025-07-06 11:46:22,030 log 66423 8400735296 Bad Request: /api/v1/auth/change-password/
WARNING 2025-07-06 11:46:23,210 log 66423 8400735296 Bad Request: /api/v1/auth/change-password/
WARNING 2025-07-06 11:46:23,600 log 66423 8400735296 Unauthorized: /api/v1/auth/profile/
WARNING 2025-07-06 11:46:24,399 log 66423 8400735296 Bad Request: /api/v1/auth/login/
WARNING 2025-07-06 11:46:24,789 log 66423 8400735296 Bad Request: /api/v1/auth/login/
WARNING 2025-07-06 11:46:25,594 log 66423 8400735296 Unauthorized: /api/v1/auth/logout/
WARNING 2025-07-06 11:46:25,791 log 66423 8400735296 Bad Request: /api/v1/auth/register/
WARNING 2025-07-06 11:46:25,987 log 66423 8400735296 Bad Request: /api/v1/auth/register/
INFO 2025-07-06 11:46:35,447 autoreload 66476 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:48:04,906 basehttp 66476 6202503168 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-06 11:48:27,413 autoreload 67282 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:52:22,130 autoreload 66476 8400735296 /Users/<USER>/Documents/augment-projects/ai-partner/backend/urls.py changed, reloading.
INFO 2025-07-06 11:52:22,552 autoreload 68597 8400735296 Watching for file changes with StatReloader
INFO 2025-07-06 11:53:45,542 autoreload 68959 8400735296 Watching for file changes with StatReloader
