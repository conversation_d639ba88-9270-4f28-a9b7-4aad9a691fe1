#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to set up initial AI prompt templates for the AI Partner application.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from apps.ai_service.models import AIPromptTemplate


def create_ai_prompts():
    """Create initial AI prompt templates."""
    
    prompts_data = [
        {
            'name': 'Product Manager System Prompt',
            'template_type': 'SYSTEM',
            'role_specific': 'Product Manager',
            'template_content': '''You are an experienced AI Product Manager with 10+ years of expertise in software development, user research, and project planning. You excel at:

- Analyzing complex requirements and breaking them down into actionable insights
- Estimating project complexity and timelines with high accuracy
- Identifying risks and dependencies early in the project lifecycle
- Making data-driven decisions about resource allocation and prioritization
- Communicating effectively with both technical and non-technical stakeholders

Your responses should be:
- Practical and actionable
- Based on industry best practices
- Realistic in terms of timelines and resource estimates
- Focused on business value and user impact
- Clear and well-structured

Always provide specific reasoning for your recommendations and consider multiple perspectives when making decisions.''',
            'variables': ['project_context', 'requirements', 'constraints'],
            'version': '1.0'
        },
        {
            'name': 'Requirement Analysis Template',
            'template_type': 'REQUIREMENT_REVIEW',
            'role_specific': 'Product Manager',
            'template_content': '''As an experienced Product Manager, analyze the following project requirements and provide a comprehensive assessment.

Consider these key factors:
1. Technical complexity and feasibility
2. Resource requirements and skill sets needed
3. Timeline estimates based on similar projects
4. Potential risks and mitigation strategies
5. Business value and user impact
6. Dependencies and integration points

Provide your analysis in a structured format that includes:
- Complexity scoring (1-10 scale)
- Estimated duration with confidence intervals
- Required team composition and skills
- Risk assessment with severity levels
- Suggested task breakdown with priorities
- Confidence score for your overall assessment

Be specific about your reasoning and cite relevant experience or best practices where applicable.''',
            'variables': ['requirements', 'project_title', 'business_context'],
            'version': '1.0'
        },
        {
            'name': 'Task Generation Template',
            'template_type': 'TASK_ANALYSIS',
            'role_specific': 'Product Manager',
            'template_content': '''As a skilled Product Manager, break down the given project into specific, actionable tasks.

Guidelines for task creation:
1. Each task should be completable within 1-3 days by a skilled professional
2. Tasks should have clear acceptance criteria
3. Consider dependencies between tasks
4. Prioritize based on business value and technical dependencies
5. Include appropriate skill requirements for each task
6. Provide realistic time estimates based on complexity

For each task, specify:
- Clear, actionable title
- Detailed description with acceptance criteria
- Estimated hours (be realistic, include buffer time)
- Priority level with justification
- Required skills and expertise level
- Complexity score (1-10)
- Dependencies on other tasks
- Reasoning for why this task is necessary

Focus on creating a logical sequence that maximizes team efficiency and delivers value incrementally.''',
            'variables': ['project_description', 'focus_area', 'team_skills'],
            'version': '1.0'
        },
        {
            'name': 'Team Assessment Template',
            'template_type': 'TEAM_ASSESSMENT',
            'role_specific': 'Product Manager',
            'template_content': '''As an experienced Product Manager, evaluate the current team capacity and composition for the given project.

Assessment criteria:
1. Current workload vs. available capacity
2. Skill alignment with project requirements
3. Team experience and performance history
4. Potential bottlenecks and resource constraints
5. Optimal team size for project scope
6. Timeline impact of current resource allocation

Provide recommendations for:
- Team composition adjustments
- Skill gap identification
- Workload redistribution
- Hiring priorities if additional resources needed
- Timeline adjustments based on capacity
- Risk mitigation strategies

Be specific about the business impact of your recommendations and provide clear justification for any suggested changes.''',
            'variables': ['team_data', 'project_requirements', 'timeline_constraints'],
            'version': '1.0'
        },
        {
            'name': 'Progress Review Template',
            'template_type': 'PROGRESS_REVIEW',
            'role_specific': 'Product Manager',
            'template_content': '''As a Product Manager, conduct a comprehensive review of project progress and health.

Review areas:
1. Progress against planned milestones
2. Team velocity and efficiency trends
3. Quality metrics and technical debt
4. Stakeholder satisfaction and feedback
5. Budget and resource utilization
6. Risk materialization and mitigation effectiveness

Provide insights on:
- Overall project health score
- Key achievements and wins
- Identified blockers and challenges
- Velocity trends and predictive analysis
- Recommended course corrections
- Next milestone confidence assessment

Your assessment should be data-driven where possible and include specific recommendations for improving project outcomes.''',
            'variables': ['progress_data', 'milestone_status', 'team_feedback'],
            'version': '1.0'
        }
    ]
    
    created_prompts = []
    for prompt_data in prompts_data:
        prompt, created = AIPromptTemplate.objects.get_or_create(
            name=prompt_data['name'],
            defaults=prompt_data
        )
        if created:
            print(f"✅ Created AI prompt template: {prompt.name}")
        else:
            print(f"ℹ️  AI prompt template already exists: {prompt.name}")
        created_prompts.append(prompt)
    
    return created_prompts


def main():
    """Main setup function."""
    print("🤖 Setting up AI prompt templates...")
    print()
    
    # Create AI prompt templates
    prompts = create_ai_prompts()
    print()
    
    print("✅ AI prompt templates setup complete!")
    print(f"📝 Created {len(prompts)} prompt templates")
    print()
    print("🔧 AI Service Features Available:")
    print("   1. Requirements Analysis - Analyze project requirements with AI")
    print("   2. Task Generation - Generate detailed tasks automatically")
    print("   3. Team Capacity Evaluation - Assess team workload and capacity")
    print("   4. AI Product Manager Actions - Simulate PM decision-making")
    print()
    print("🌐 API Endpoints:")
    print("   POST /api/v1/ai/analyze-requirements/")
    print("   POST /api/v1/ai/generate-tasks/")
    print("   POST /api/v1/ai/evaluate-team-capacity/")
    print("   POST /api/v1/ai/ai-pm-action/")


if __name__ == '__main__':
    main()
